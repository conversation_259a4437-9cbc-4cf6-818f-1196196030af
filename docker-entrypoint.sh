#!/bin/bash
set -e

# 设置默认环境
export DEPLOY_ENV=${DEPLOY_ENV:-production}
export BUILD_TIME=${BUILD_TIME:-$(date -u +"%Y-%m-%dT%H:%M:%SZ")}
export APP_VERSION=${APP_VERSION:-latest}

echo "🚀 启动容器 - 环境: $DEPLOY_ENV"
echo "📅 构建时间: $BUILD_TIME"
echo "🏷️  应用版本: $APP_VERSION"

# 显示环境变量
echo "🔧 环境变量配置:"
echo "  DEPLOY_ENV: $DEPLOY_ENV"
echo "  API_URL: ${API_URL:-未设置}"
echo "  APP_URL: ${APP_URL:-未设置}"
echo "  SSO_URL: ${SSO_URL:-未设置}"
echo "  CLIENT_ID: ${CLIENT_ID:-未设置}"
echo "  UPLOAD_URL: ${UPLOAD_URL:-未设置}"

# 运行配置替换脚本
echo "🔄 执行运行时配置替换..."
/usr/local/bin/config-replacer.sh

# 创建nginx配置文件
echo "📝 生成nginx配置文件..."
# 使用sed替换环境变量
sed -e "s/\${DEPLOY_ENV}/$DEPLOY_ENV/g" \
    -e "s/\${BUILD_TIME}/$BUILD_TIME/g" \
    -e "s/\${APP_VERSION}/$APP_VERSION/g" \
    /etc/nginx/nginx.conf.template > /etc/nginx/nginx.conf

# 根据环境配置API代理
if [ ! -z "$API_PROXY_URL" ]; then
    echo "🔗 配置API代理: $API_PROXY_URL"
    
    # 生成代理配置
    PROXY_CONFIG="proxy_pass $API_PROXY_URL;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;"
    
    # 替换占位符
    sed -i "s|# PROXY_CONFIG_PLACEHOLDER|$PROXY_CONFIG|g" /etc/nginx/nginx.conf
else
    echo "ℹ️  未配置API代理"
    # 移除代理配置占位符
    sed -i "/# PROXY_CONFIG_PLACEHOLDER/d" /etc/nginx/nginx.conf
fi

# 环境特定配置
case "$DEPLOY_ENV" in
    "development")
        echo "🔧 开发环境配置"
        # 开发环境：启用详细日志，禁用缓存
        sed -i 's/error_log \/var\/log\/nginx\/error.log warn;/error_log \/var\/log\/nginx\/error.log debug;/' /etc/nginx/nginx.conf
        ;;
    "test"|"staging")
        echo "🧪 测试环境配置"
        # 测试环境：中等日志级别
        sed -i 's/error_log \/var\/log\/nginx\/error.log warn;/error_log \/var\/log\/nginx\/error.log info;/' /etc/nginx/nginx.conf
        ;;
    "production")
        echo "🏭 生产环境配置"
        # 生产环境：保持默认配置
        ;;
    *)
        echo "⚠️  未知环境: $DEPLOY_ENV，使用生产环境配置"
        ;;
esac

# 验证nginx配置
echo "✅ 验证nginx配置..."
nginx -t

# 创建日志目录
mkdir -p /var/log/nginx

# 显示最终的应用配置
echo "📋 最终应用配置:"
if [ -f "/usr/share/nginx/html/dist/_app.config.js" ]; then
    echo "  配置文件已生成: _app.config.js"
else
    echo "  ⚠️  配置文件生成失败"
fi

echo "🎉 配置完成，启动nginx..."

# 执行传入的命令
exec "$@"
