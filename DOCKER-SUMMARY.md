# Docker 多环境部署方案总结

## 🎯 方案特点

✅ **一次构建，多环境部署**  
✅ **运行时环境变量配置**  
✅ **CI/CD平台友好**  
✅ **无需手动构建脚本**  

## 📁 核心文件

```
├── Dockerfile                # 多环境构建配置
├── docker-entrypoint.sh      # 容器启动脚本
├── config-replacer.sh        # 运行时配置替换（Shell版本）
├── nginx.conf.template       # Nginx配置模板
├── docker-compose.yml        # 本地测试用
├── DOCKER-README.md          # 部署说明
└── ENV-CONFIG-REFERENCE.md   # 环境变量参考
```

## 🏗️ CI/CD 使用

### 构建
```bash
docker build -t cr.ttyuyin.com/itmp/itmp-frontend:${BUILD_TAG} .
```

### 部署
```bash
# 开发环境
docker run -d -e DEPLOY_ENV=development cr.ttyuyin.com/itmp/itmp-frontend:${BUILD_TAG}

# 生产环境  
docker run -d -e DEPLOY_ENV=production cr.ttyuyin.com/itmp/itmp-frontend:${BUILD_TAG}
```

## 🔧 工作原理

1. **构建阶段**: 使用 `pnpm build:docker` 构建包含占位符的应用
2. **运行时**: 根据 `DEPLOY_ENV` 选择对应环境配置
3. **配置替换**: 自动替换前端代码中的配置占位符，生成正确的全局变量
4. **变量名生成**: 根据APP_TITLE生成动态变量名（如：`__PRODUCTION__592779B90020002D002095EE98987BA174065E7353F0__CONF__`）
5. **备用机制**: 同时生成备用变量名 `__PRODUCTION____APP__CONF__` 确保兼容性
6. **前端适配**: 前端代码支持多种变量名查找，确保配置正确加载
7. **Nginx配置**: 根据环境变量生成nginx配置

## 📋 环境配置

| 环境 | DEPLOY_ENV | 端口 | API地址 |
|------|------------|------|---------|
| 开发 | development | 3001 | http://itmp-test.ttyuyin.com/api |
| 测试 | test | 3002 | http://itmp-test.ttyuyin.com/api |
| 生产 | production | 80 | https://itmp.ttyuyin.com/api |

## 🔍 验证

```bash
curl http://localhost/health      # 健康检查
curl http://localhost/env-info    # 环境信息
```

## 📚 详细文档

- [DOCKER-README.md](./DOCKER-README.md) - 部署说明
- [ENV-CONFIG-REFERENCE.md](./ENV-CONFIG-REFERENCE.md) - 环境变量参考

---

**完美实现了您的需求：一次构建，运行时通过环境变量识别生产或开发环境！**
