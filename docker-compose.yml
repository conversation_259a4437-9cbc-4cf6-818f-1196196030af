# 本地多环境测试用 Docker Compose
# 注意：生产环境请使用CI/CD平台部署
version: '3.8'

services:
  # 开发环境
  itmp-frontend-dev:
    image: cr.ttyuyin.com/itmp/itmp-frontend:latest
    container_name: itmp-frontend-dev
    ports:
      - "3001:80"
    environment:
      - DEPLOY_ENV=development
      - API_URL=http://itmp-test.ttyuyin.com/api
      - APP_URL=http://localhost:3001
      - SSO_URL=test-ebc-sso.52tt.com
      - CLIENT_ID=itmp-dev
    restart: unless-stopped

  # 测试环境
  itmp-frontend-test:
    image: cr.ttyuyin.com/itmp/itmp-frontend:latest
    container_name: itmp-frontend-test
    ports:
      - "3002:80"
    environment:
      - DEPLOY_ENV=test
      - API_URL=http://itmp-test.ttyuyin.com/api
      - APP_URL=http://test.itmp.ttyuyin.com
      - SSO_URL=test-ebc-sso.52tt.com
      - CLIENT_ID=itmp-test
    restart: unless-stopped

  # 生产环境
  itmp-frontend-prod:
    image: cr.ttyuyin.com/itmp/itmp-frontend:latest
    container_name: itmp-frontend-prod
    ports:
      - "3003:80"
    environment:
      - DEPLOY_ENV=production
      - API_URL=https://itmp.ttyuyin.com/api
      - APP_URL=https://itmp.ttyuyin.com
      - SSO_URL=ebc-sso.52tt.com
      - CLIENT_ID=itmp
    restart: unless-stopped
