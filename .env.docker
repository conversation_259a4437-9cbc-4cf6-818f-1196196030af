# Whether to open mock
VITE_USE_MOCK = false

# App title (will be replaced at runtime)
VITE_GLOB_APP_TITLE = "__VITE_GLOB_APP_TITLE__"

# public path (will be replaced at runtime)
VITE_GLOB_PUBLIC_PATH = "__VITE_GLOB_PUBLIC_PATH__"

# timeout(seconds)
VITE_TIMEOUT = 15
# Delete console
VITE_DROP_CONSOLE = true

# Whether to enable gzip or brotli compression
# Optional: gzip | brotli | none
# If you need multiple forms, you can use `,` to separate
VITE_BUILD_COMPRESS = 'none'

# API URL (will be replaced at runtime)
VITE_GLOB_API_URL="__VITE_GLOB_API_URL__"

# Application base URL (will be replaced at runtime)
VITE_GLOB_URL="__VITE_GLOB_URL__"

# SSO server URL (will be replaced at runtime)
VITE_GLOB_SSO="__VITE_GLOB_SSO__"

# Client ID (will be replaced at runtime)
VITE_GLOB_CLIENT_ID="__VITE_GLOB_CLIENT_ID__"

# File upload address (will be replaced at runtime)
VITE_GLOB_UPLOAD_URL="__VITE_GLOB_UPLOAD_URL__"

# Interface prefix (will be replaced at runtime)
VITE_GLOB_API_URL_PREFIX="__VITE_GLOB_API_URL_PREFIX__"
