import type { GlobEnvConfig } from '#/config';

/**
 * 运行时环境配置管理工具
 * 支持单一镜像多环境部署，通过环境变量动态配置
 */

// 环境变量映射表
const ENV_MAPPING = {
  // 基础配置
  VITE_GLOB_APP_TITLE: 'APP_TITLE',
  VITE_GLOB_PUBLIC_PATH: 'PUBLIC_PATH',
  
  // API 相关
  VITE_GLOB_API_URL: 'API_URL',
  VITE_GLOB_API_URL_PREFIX: 'API_URL_PREFIX',
  VITE_GLOB_UPLOAD_URL: 'UPLOAD_URL',
  
  // 应用相关
  VITE_GLOB_URL: 'APP_URL',
  VITE_GLOB_SSO: 'SSO_URL',
  VITE_GLOB_CLIENT_ID: 'CLIENT_ID',
} as const;

// 默认配置
const DEFAULT_CONFIG: Partial<GlobEnvConfig> = {
  VITE_GLOB_APP_TITLE: '大禹 - 问题管理平台',
  VITE_GLOB_PUBLIC_PATH: '/',
  VITE_GLOB_API_URL: '/api',
  VITE_GLOB_API_URL_PREFIX: '',
  VITE_GLOB_UPLOAD_URL: '/upload',
  VITE_GLOB_URL: '',
  VITE_GLOB_SSO: '',
  VITE_GLOB_CLIENT_ID: '',
};

// 环境配置映射
const ENV_CONFIG_MAP = {
  production: {
    VITE_GLOB_URL: 'https://itmp.ttyuyin.com',
    VITE_GLOB_SSO: 'ebc-sso.52tt.com',
    VITE_GLOB_CLIENT_ID: 'itmp',
  },
  development: {
    VITE_GLOB_URL: 'http://itmp-test.ttyuyin.com',
    VITE_GLOB_SSO: 'test-ebc-sso.52tt.com',
    VITE_GLOB_CLIENT_ID: 'itmp-test',
  },
  test: {
    VITE_GLOB_URL: 'http://itmp-test.ttyuyin.com',
    VITE_GLOB_SSO: 'test-ebc-sso.52tt.com',
    VITE_GLOB_CLIENT_ID: 'itmp-test',
  },
  staging: {
    VITE_GLOB_URL: 'http://itmp-test.ttyuyin.com',
    VITE_GLOB_SSO: 'test-ebc-sso.52tt.com',
    VITE_GLOB_CLIENT_ID: 'itmp-test',
  },
} as const;

/**
 * 获取当前部署环境
 */
export function getDeployEnv(): string {
  // 优先从运行时环境变量获取
  if (typeof window !== 'undefined' && window.__APP_INFO__?.deployEnv) {
    return window.__APP_INFO__.deployEnv;
  }
  
  // 从构建时环境变量获取
  if (typeof import !== 'undefined' && import.meta?.env?.MODE) {
    return import.meta.env.MODE;
  }
  
  // 默认为生产环境
  return 'production';
}

/**
 * 从运行时环境变量获取配置
 */
export function getRuntimeEnvConfig(): Partial<GlobEnvConfig> {
  const config: Partial<GlobEnvConfig> = {};
  
  // 如果在浏览器环境中，尝试从环境变量获取配置
  if (typeof window !== 'undefined') {
    Object.entries(ENV_MAPPING).forEach(([viteKey, envKey]) => {
      // 从 window 对象获取运行时注入的环境变量
      const value = (window as any)[`__RUNTIME_${envKey}__`];
      if (value !== undefined && value !== '') {
        (config as any)[viteKey] = value;
      }
    });
  }
  
  return config;
}

/**
 * 获取环境默认配置
 */
export function getEnvDefaultConfig(env?: string): Partial<GlobEnvConfig> {
  const deployEnv = env || getDeployEnv();
  return ENV_CONFIG_MAP[deployEnv as keyof typeof ENV_CONFIG_MAP] || ENV_CONFIG_MAP.production;
}

/**
 * 合并配置优先级：运行时环境变量 > 环境默认配置 > 全局默认配置
 */
export function mergeRuntimeConfig(baseConfig: Partial<GlobEnvConfig> = {}): GlobEnvConfig {
  const deployEnv = getDeployEnv();
  const runtimeConfig = getRuntimeEnvConfig();
  const envDefaultConfig = getEnvDefaultConfig(deployEnv);
  
  const mergedConfig = {
    ...DEFAULT_CONFIG,
    ...envDefaultConfig,
    ...baseConfig,
    ...runtimeConfig,
  } as GlobEnvConfig;
  
  console.log(`🔧 运行时配置合并完成 - 环境: ${deployEnv}`, {
    runtime: runtimeConfig,
    envDefault: envDefaultConfig,
    merged: mergedConfig,
  });
  
  return mergedConfig;
}

/**
 * 检查配置是否完整
 */
export function validateConfig(config: Partial<GlobEnvConfig>): boolean {
  const requiredKeys = ['VITE_GLOB_APP_TITLE', 'VITE_GLOB_API_URL'];
  
  for (const key of requiredKeys) {
    if (!config[key as keyof GlobEnvConfig]) {
      console.warn(`⚠️ 缺少必要配置: ${key}`);
      return false;
    }
  }
  
  return true;
}

/**
 * 获取完整的运行时配置
 */
export function getFullRuntimeConfig(baseConfig?: Partial<GlobEnvConfig>): GlobEnvConfig {
  const config = mergeRuntimeConfig(baseConfig);
  
  if (!validateConfig(config)) {
    console.warn('⚠️ 配置验证失败，使用默认配置');
  }
  
  return config;
}

// 导出类型
export type { GlobEnvConfig };
export type DeployEnv = keyof typeof ENV_CONFIG_MAP;
