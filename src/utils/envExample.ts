/**
 * 环境配置使用示例
 * 展示如何在代码中使用运行时环境配置
 */

import { getAppEnvConfig } from './env';
import { getFullRuntimeConfig, getDeployEnv } from './runtimeConfig';

/**
 * 获取API基础URL
 */
export function getApiBaseUrl(): string {
  const config = getAppEnvConfig();
  return config.VITE_GLOB_API_URL || '/api';
}

/**
 * 获取应用基础URL
 */
export function getAppBaseUrl(): string {
  const config = getAppEnvConfig();
  return config.VITE_GLOB_URL || '';
}

/**
 * 获取SSO服务器URL
 */
export function getSSOUrl(): string {
  const config = getAppEnvConfig();
  return config.VITE_GLOB_SSO || '';
}

/**
 * 获取客户端ID
 */
export function getClientId(): string {
  const config = getAppEnvConfig();
  return config.VITE_GLOB_CLIENT_ID || '';
}

/**
 * 获取上传URL
 */
export function getUploadUrl(): string {
  const config = getAppEnvConfig();
  return config.VITE_GLOB_UPLOAD_URL || '/upload';
}

/**
 * 获取应用标题
 */
export function getAppTitle(): string {
  const config = getAppEnvConfig();
  return config.VITE_GLOB_APP_TITLE || '大禹 - 问题管理平台';
}

/**
 * 检查是否为生产环境
 */
export function isProduction(): boolean {
  const env = getDeployEnv();
  return env === 'production';
}

/**
 * 检查是否为测试环境
 */
export function isTest(): boolean {
  const env = getDeployEnv();
  return env === 'test' || env === 'staging';
}

/**
 * 检查是否为开发环境
 */
export function isDevelopment(): boolean {
  const env = getDeployEnv();
  return env === 'development';
}

/**
 * 获取完整的环境配置信息（用于调试）
 */
export function getEnvInfo() {
  const config = getAppEnvConfig();
  const deployEnv = getDeployEnv();
  
  return {
    deployEnv,
    config,
    isProduction: isProduction(),
    isTest: isTest(),
    isDevelopment: isDevelopment(),
  };
}

/**
 * 打印环境配置信息（用于调试）
 */
export function logEnvInfo() {
  const info = getEnvInfo();
  console.group('🔧 环境配置信息');
  console.log('部署环境:', info.deployEnv);
  console.log('是否生产环境:', info.isProduction);
  console.log('是否测试环境:', info.isTest);
  console.log('是否开发环境:', info.isDevelopment);
  console.log('配置详情:', info.config);
  console.groupEnd();
}

// 在开发环境下自动打印配置信息
if (typeof window !== 'undefined' && isDevelopment()) {
  // 延迟执行，确保配置已加载
  setTimeout(() => {
    logEnvInfo();
  }, 1000);
}
