import type { GlobEnvConfig } from '#/config';
import pkg from '../../package.json';
import { API_ADDRESS } from '@/enums/cacheEnum';

export function getCommonStoragePrefix() {
  const { VITE_GLOB_APP_TITLE } = getAppEnvConfig();
  return `${VITE_GLOB_APP_TITLE.replace(/\s/g, '_')}__${getEnv()}`.toUpperCase();
}

// Generate cache key according to version
export function getStorageShortName() {
  return `${getCommonStoragePrefix()}${`__${pkg.version}`}__`.toUpperCase();
}

const getVariableName = (title: string) => {
  function strToHex(str: string) {
    const result: string[] = [];
    for (let i = 0; i < str.length; ++i) {
      const hex = str.charCodeAt(i).toString(16);
      result.push(('000' + hex).slice(-4));
    }
    return result.join('').toUpperCase();
  }
  return `__PRODUCTION__${strToHex(title) || '__APP'}__CONF__`.toUpperCase().replace(/\s/g, '');
};

export function getAppEnvConfig() {
  // 在生产环境中，优先使用默认的APP_TITLE来生成变量名
  const defaultTitle = '大禹 - 问题管理平台';
  const appTitle = import.meta.env.VITE_GLOB_APP_TITLE || defaultTitle;
  const ENV_NAME = getVariableName(appTitle);

  let ENV: GlobEnvConfig;

  if (import.meta.env.DEV) {
    // 开发环境：直接使用 import.meta.env
    ENV = import.meta.env as unknown as GlobEnvConfig;
  } else {
    // 生产环境：从 window 对象获取配置
    ENV = (window[ENV_NAME] as unknown as GlobEnvConfig);

    // 如果主变量名找不到，尝试备用变量名
    if (!ENV) {
      const fallbackNames = [
        getVariableName(defaultTitle), // 使用默认标题
        '__PRODUCTION____APP__CONF__', // 固定备用名称
      ];

      for (const fallbackName of fallbackNames) {
        if (window[fallbackName]) {
          ENV = window[fallbackName] as unknown as GlobEnvConfig;
          console.log(`🔧 使用备用配置变量: ${fallbackName}`);
          break;
        }
      }
    }

    // 如果仍然找不到配置，使用默认值
    if (!ENV) {
      console.warn('⚠️ 未找到全局配置，使用默认值');
      ENV = {
        VITE_GLOB_APP_TITLE: defaultTitle,
        VITE_GLOB_API_URL: '/api',
        VITE_GLOB_API_URL_PREFIX: '',
        VITE_GLOB_UPLOAD_URL: '/upload',
      };
    }
  }

  const { VITE_GLOB_APP_TITLE, VITE_GLOB_API_URL_PREFIX, VITE_GLOB_UPLOAD_URL } = ENV;
  let { VITE_GLOB_API_URL } = ENV;

  if (localStorage.getItem(API_ADDRESS)) {
    const address = JSON.parse(localStorage.getItem(API_ADDRESS) || '{}');
    if (address?.key) VITE_GLOB_API_URL = address?.val;
  }

  return {
    VITE_GLOB_APP_TITLE,
    VITE_GLOB_API_URL,
    VITE_GLOB_API_URL_PREFIX,
    VITE_GLOB_UPLOAD_URL,
  };
}

/**
 * @description: Development mode
 */
export const devMode = 'development';

/**
 * @description: Production mode
 */
export const prodMode = 'production';

/**
 * @description: Get environment variables
 * @returns:
 * @example:
 */
export function getEnv(): string {
  return import.meta.env.MODE;
}

/**
 * @description: Is it a development mode
 * @returns:
 * @example:
 */
export function isDevMode(): boolean {
  return import.meta.env.DEV;
}

/**
 * @description: Is it a production mode
 * @returns:
 * @example:
 */
export function isProdMode(): boolean {
  return import.meta.env.PROD;
}
