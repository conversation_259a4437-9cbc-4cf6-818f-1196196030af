import { FaultParams, FaultListGetResultModel } from './model/faultModel';
import { defHttp } from '@/utils/http/axios';
import dayjs from 'dayjs';

import { useUserStore } from '@/store/modules/user';

const userStore = useUserStore();
const userInfo = userStore.getUserInfo;

enum Api {
  // 故障列表
  getFaultList = '/V2/fault/selectFault/',

  // 故障数据
  getDashboardDataAPI = '/V2/fault/dashboard/',

  // 故障图表数据
  getChartLineDataAPI = '/V2/fault/chartline/',

  // 创建故障
  createFault = '/V2/fault/createFault/',
  // 编辑故障
  editFault = '/V2/fault/editFault/',
  // 删除故障
  deleteFaultAPI = '/V2/fault/deleteFault/',

  // 获取故障预设列表
  selectFilterTreeAPI = '/V2/fault/selectFilterTree/',

  // 编辑故障预设列表
  editFilterTreeAPI = '/V2/fault/editFilterTree/',

  // 创建预设节点
  editFilterTreeNodeAPI = '/V2/fault/editFilterTreeNode/',
  // 获取预设节点
  selectFilterTreeNodeAPI = '/V2/fault/selectFilterTreeNode/',
  // // 创建故障预设
  // createFaultPresetAPI = '/V2/fault/createFaultPreset/',
  // // 编辑故障预设
  // editFaultPresetAPI = '/V2/fault/editFaultPreset/',
  // // 删除故障预设
  // deleteFaultPresetAPI = '/V2/fault/deleteFaultPreset/',

  // 提取整改措施
  extractRectifyInfo = '/dify/workflows/run/extract_rectify_info/',
  // 故障报告分析
  faultReportAnalysis = '/api/dify/workflows/run/fault_report_analysis/',
}

// 获取故障列表
export const getFaultList = (params) =>
  defHttp.post<FaultListGetResultModel>({
    url: Api.getFaultList,
    params: { data: params },
  });

// 统计数据
export const getDashboardDataAPI = (params) =>
  defHttp.post({
    url: Api.getDashboardDataAPI,
    params: { data: params },
  });

// 图表数据
export const getChartLineDataAPI = (params) =>
  defHttp.post({
    url: Api.getChartLineDataAPI,
    params: { data: params },
  });

// 创建故障
export const createFaultApi = () =>
  defHttp.post({
    url: Api.createFault,
    params: {
      data: {
        faultDescribe:
          dayjs().format('YYYY-MM-DD') + ' 手动创建故障报告（Driver：' + userInfo.user_name + '）',
        driver: userInfo.user_name,
        startTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        currentProgress: '未复盘',
        reportType: '故障',
      },
    },
  });

// 编辑故障
export const editFaultApi = (params) =>
  defHttp.post({ url: Api.editFault, params: { data: params } });

// 删除故障
export const deleteFaultAPI = (params) =>
  defHttp.post({ url: Api.deleteFaultAPI, params: { data: params } });

// 获取故障预设列表
export const selectFilterTreeAPI = () => defHttp.get({ url: Api.selectFilterTreeAPI });
// 编辑故障预设列表
export const editFilterTreeAPI = (params) =>
  defHttp.post({ url: Api.editFilterTreeAPI, params: { data: params, id: 0 } });

// 创建故障预设节点
export const editFilterTreeNodeAPI = (params) =>
  defHttp.post({ url: Api.editFilterTreeNodeAPI, params: { data: params } });
// 获取故障预设节点
export const selectFilterTreeNodeAPI = (params) =>
  defHttp.post({ url: Api.selectFilterTreeNodeAPI, params: { data: params } });

// 提取整改措施
export const extractRectifyInfoAPI = (params) =>
  defHttp.post({
    url: Api.extractRectifyInfo,
    params: { ...params },
  });

// 故障报告分析
export const faultReportAnalysisAPI = (params) =>
  defHttp.post({
    url: Api.faultReportAnalysis,
    params: { ...params },
  });
