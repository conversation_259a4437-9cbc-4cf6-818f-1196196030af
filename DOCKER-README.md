# Docker 多环境部署方案

本项目支持**一次构建，多环境部署**，通过运行时环境变量区分不同环境配置。

## 🏗️ 构建

```bash
# CI/CD平台构建命令
docker build -t cr.ttyuyin.com/itmp/itmp-frontend:${BUILD_TAG} .
```

## 🚀 部署

### 环境变量配置

| 环境 | DEPLOY_ENV | API_URL | SSO_URL |
|------|------------|---------|---------|
| 开发 | development | http://itmp-test.ttyuyin.com/api | test-ebc-sso.52tt.com |
| 测试 | test | http://itmp-test.ttyuyin.com/api | test-ebc-sso.52tt.com |
| 生产 | production | https://itmp.ttyuyin.com/api | ebc-sso.52tt.com |

### 部署命令

```bash
# 开发环境
docker run -d \
  --name itmp-frontend-dev \
  -p 3001:80 \
  -e DEPLOY_ENV=development \
  -e API_URL=http://itmp-test.ttyuyin.com/api \
  -e SSO_URL=test-ebc-sso.52tt.com \
  -e CLIENT_ID=itmp-dev \
  cr.ttyuyin.com/itmp/itmp-frontend:${BUILD_TAG}

# 测试环境
docker run -d \
  --name itmp-frontend-test \
  -p 3002:80 \
  -e DEPLOY_ENV=test \
  -e API_URL=http://itmp-test.ttyuyin.com/api \
  -e SSO_URL=test-ebc-sso.52tt.com \
  -e CLIENT_ID=itmp-test \
  cr.ttyuyin.com/itmp/itmp-frontend:${BUILD_TAG}

# 生产环境
docker run -d \
  --name itmp-frontend-prod \
  -p 80:80 \
  -e DEPLOY_ENV=production \
  -e API_URL=https://itmp.ttyuyin.com/api \
  -e SSO_URL=ebc-sso.52tt.com \
  -e CLIENT_ID=itmp \
  cr.ttyuyin.com/itmp/itmp-frontend:${BUILD_TAG}
```

## 🔍 健康检查

```bash
# 健康检查
curl http://localhost/health

# 环境信息
curl http://localhost/env-info
```

## ☸️ Kubernetes 示例

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: itmp-frontend-prod
spec:
  replicas: 3
  selector:
    matchLabels:
      app: itmp-frontend-prod
  template:
    metadata:
      labels:
        app: itmp-frontend-prod
    spec:
      containers:
      - name: itmp-frontend
        image: cr.ttyuyin.com/itmp/itmp-frontend:latest
        ports:
        - containerPort: 80
        env:
        - name: DEPLOY_ENV
          value: "production"
        - name: API_URL
          value: "https://itmp.ttyuyin.com/api"
        - name: SSO_URL
          value: "ebc-sso.52tt.com"
        - name: CLIENT_ID
          value: "itmp"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
```

## 📝 环境变量说明

### 必需变量
- `DEPLOY_ENV`: 部署环境 (development/test/production)

### 可选变量
- `API_URL`: API服务地址
- `APP_URL`: 应用访问地址
- `SSO_URL`: SSO服务地址
- `CLIENT_ID`: 客户端ID
- `UPLOAD_URL`: 文件上传地址
- `APP_VERSION`: 应用版本号

详细配置参考：[ENV-CONFIG-REFERENCE.md](./ENV-CONFIG-REFERENCE.md)
