{"name": "vben-admin", "version": "2.10.1", "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": {"url": "https://github.com/vbenjs/vue-vben-admin/issues"}, "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git"}, "license": "MIT", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "type": "module", "scripts": {"bootstrap": "pnpm install", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 pnpm vite build --mode production", "build:dev": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=8192 pnpm vite build --mode development", "build:analyze": "cross-env NODE_OPTIONS=--max-old-space-size=8192 pnpm vite build --mode analyze", "build:docker": "vite build --mode docker", "build:no-cache": "pnpm store prune && npm run build", "build:test": "cross-env NODE_OPTIONS=--max-old-space-size=8192 pnpm vite build --mode test", "commit": "czg", "dev": "pnpm vite", "preinstall": "npx only-allow pnpm", "postinstall": "turbo run stub", "lint": "turbo run lint", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write .", "lint:stylelint": "stylelint \"**/*.{vue,css,less,scss}\" --fix --cache --cache-location node_modules/.cache/stylelint/", "prepare": "husky install", "preview": "npm run build && vite preview", "reinstall": "rimraf pnpm-lock.yaml && rimraf package.lock.json && rimraf node_modules && npm run bootstrap", "serve": "npm run dev", "test:gzip": "npx http-server dist --cors --gzip -c-1", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"], "{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write--parser json"], "package.json": ["prettier --write"], "*.vue": ["prettier --write", "eslint --fix", "stylelint --fix"], "*.{scss,less,styl,html}": ["prettier --write", "stylelint --fix"], "*.md": ["prettier --write"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@iconify/iconify": "^3.1.1", "@logicflow/core": "^1.2.18", "@logicflow/extension": "^1.2.19", "@types/markdown-it": "^14.1.2", "@vben/hooks": "workspace:*", "@videojs-player/vue": "^1.0.0", "@vue/shared": "^3.4.5", "@vueuse/core": "^10.7.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "@wangeditor/plugin-mention": "^1.0.0", "@zxcvbn-ts/core": "^3.0.4", "ant-design-vue": "^4.0.8", "ant-design-x-vue": "^1.2.7", "axios": "^1.6.4", "clipboard-polyfill": "^4.0.2", "codemirror": "^5.65.16", "cropperjs": "^1.6.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "dom-to-image": "^2.6.0", "downloadjs": "^1.4.7", "driver.js": "^1.3.1", "echarts": "^5.4.3", "exceljs": "^4.4.0", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "husky": "^9.1.5", "jspdf": "^2.5.1", "jszip": "^3.10.1", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "mockjs": "^1.1.0", "mpegts.js": "^1.7.3", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.1", "pinia": "2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "print-js": "^1.6.0", "qrcode": "^1.5.3", "qs": "^6.11.2", "resize-observer-polyfill": "^1.5.1", "showdown": "^2.1.0", "sortablejs": "^1.15.1", "splitpanes": "^3.1.5", "tinymce": "^5.10.9", "ua-parser-js": "^2.0.0", "unocss": "0.58.3", "vditor": "^3.9.8", "vue": "3.5.17", "vue-draggable-plus": "^0.4.0", "vue-i18n": "^9.8.0", "vue-json-pretty": "^2.3.0", "vue-matomo": "^4.2.0", "vue-router": "^4.2.5", "vue-types": "^5.1.1", "vuedraggable": "^4.1.0", "vxe-table": "^4.5.17", "vxe-table-plugin-export-xlsx": "^3.1.0", "xe-utils": "^3.5.14", "xgplayer": "^3.0.11", "xgplayer-flv": "^3.0.18", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^18.4.4", "@commitlint/config-conventional": "^18.4.4", "@iconify/json": "^2.2.164", "@purge-icons/generated": "^0.10.0", "@types/codemirror": "^5.60.15", "@types/crypto-js": "^4.2.1", "@types/lodash-es": "^4.17.12", "@types/mockjs": "^1.0.10", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.11", "@types/showdown": "^2.0.6", "@types/sortablejs": "^1.15.7", "@vben/eslint-config": "workspace:*", "@vben/stylelint-config": "workspace:*", "@vben/ts-config": "workspace:*", "@vben/types": "workspace:*", "@vben/vite-config": "workspace:*", "@vue/compiler-sfc": "^3.4.5", "@vue/test-utils": "^2.4.3", "autoprefixer": "^10.4.19", "cross-env": "^7.0.3", "cz-git": "^1.8.0", "czg": "^1.8.0", "lint-staged": "15.2.0", "prettier": "^3.1.1", "prettier-plugin-packagejson": "^2.4.8", "rimraf": "^5.0.5", "tailwindcss": "^3.4.3", "turbo": "^1.11.3", "typescript": "^5.3.3", "unbuild": "^2.0.0", "video.js": "^7.21.5", "vite": "^5.0.10", "vite-plugin-mock": "^2.9.6", "vue-tsc": "^1.8.27"}, "packageManager": "pnpm@8.10.0", "engines": {"node": ">=18.12.0", "pnpm": ">=8.10.0"}}