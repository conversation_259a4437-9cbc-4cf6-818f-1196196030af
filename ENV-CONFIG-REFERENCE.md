# 环境变量配置参考

CI/CD平台部署时的环境变量配置参考。

## 🔧 环境变量

### 必需变量
- `DEPLOY_ENV`: 部署环境标识 (development/test/staging/production)

### 可选变量
- `API_URL`: API服务地址
- `APP_URL`: 应用访问地址
- `SSO_URL`: SSO服务地址
- `CLIENT_ID`: 客户端ID
- `UPLOAD_URL`: 文件上传地址
- `APP_VERSION`: 应用版本号

## 📋 环境配置模板

### 开发环境 (development)
```bash
DEPLOY_ENV=development
API_URL=http://itmp-test.ttyuyin.com/api
APP_URL=http://dev.itmp.ttyuyin.com
SSO_URL=test-ebc-sso.52tt.com
CLIENT_ID=itmp-dev
UPLOAD_URL=/upload
```

### 测试环境 (test)
```bash
DEPLOY_ENV=test
API_URL=http://itmp-test.ttyuyin.com/api
APP_URL=http://test.itmp.ttyuyin.com
SSO_URL=test-ebc-sso.52tt.com
CLIENT_ID=itmp-test
UPLOAD_URL=/upload
```

### 预发布环境 (staging)
```bash
DEPLOY_ENV=staging
API_URL=http://staging.itmp.ttyuyin.com/api
APP_URL=http://staging.itmp.ttyuyin.com
SSO_URL=test-ebc-sso.52tt.com
CLIENT_ID=itmp-staging
UPLOAD_URL=/upload
```

### 生产环境 (production)
```bash
DEPLOY_ENV=production
API_URL=https://itmp.ttyuyin.com/api
APP_URL=https://itmp.ttyuyin.com
SSO_URL=ebc-sso.52tt.com
CLIENT_ID=itmp
UPLOAD_URL=/upload
```

## 🐳 Docker 运行示例

### 开发环境
```bash
docker run -d \
  --name itmp-frontend-dev \
  -p 3001:80 \
  -e DEPLOY_ENV=development \
  -e API_URL=http://itmp-test.ttyuyin.com/api \
  -e APP_URL=http://dev.itmp.ttyuyin.com \
  -e SSO_URL=test-ebc-sso.52tt.com \
  -e CLIENT_ID=itmp-dev \
  cr.ttyuyin.com/itmp/itmp-frontend:latest
```

### 测试环境
```bash
docker run -d \
  --name itmp-frontend-test \
  -p 3002:80 \
  -e DEPLOY_ENV=test \
  -e API_URL=http://itmp-test.ttyuyin.com/api \
  -e APP_URL=http://test.itmp.ttyuyin.com \
  -e SSO_URL=test-ebc-sso.52tt.com \
  -e CLIENT_ID=itmp-test \
  cr.ttyuyin.com/itmp/itmp-frontend:latest
```

### 生产环境
```bash
docker run -d \
  --name itmp-frontend-prod \
  -p 80:80 \
  -e DEPLOY_ENV=production \
  -e API_URL=https://itmp.ttyuyin.com/api \
  -e APP_URL=https://itmp.ttyuyin.com \
  -e SSO_URL=ebc-sso.52tt.com \
  -e CLIENT_ID=itmp \
  cr.ttyuyin.com/itmp/itmp-frontend:latest
```

## ☸️ Kubernetes ConfigMap 示例

### 开发环境
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: itmp-frontend-dev-config
data:
  DEPLOY_ENV: "development"
  API_URL: "http://itmp-test.ttyuyin.com/api"
  APP_URL: "http://dev.itmp.ttyuyin.com"
  SSO_URL: "test-ebc-sso.52tt.com"
  CLIENT_ID: "itmp-dev"
  UPLOAD_URL: "/upload"
```

### 生产环境
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: itmp-frontend-prod-config
data:
  DEPLOY_ENV: "production"
  API_URL: "https://itmp.ttyuyin.com/api"
  APP_URL: "https://itmp.ttyuyin.com"
  SSO_URL: "ebc-sso.52tt.com"
  CLIENT_ID: "itmp"
  UPLOAD_URL: "/upload"
```

## 🔍 环境验证

部署后可以通过以下端点验证环境配置：

```bash
# 健康检查
curl http://localhost/health

# 环境信息
curl http://localhost/env-info

# 配置信息（仅非生产环境）
curl http://localhost/config-info
```

预期响应：
```json
{
  "environment": "production",
  "version": "v1.0.0",
  "buildTime": "2024-01-01T00:00:00Z"
}
```

## 📝 注意事项

1. **敏感信息**: 使用CI/CD平台的密钥管理功能存储敏感配置
2. **环境隔离**: 确保不同环境使用不同的配置值
3. **版本标记**: 建议使用Git commit SHA或构建号作为APP_VERSION
4. **健康检查**: 部署后验证服务状态和配置正确性
5. **日志监控**: 关注容器启动日志，确认配置加载成功

## 🔄 配置更新

配置更新流程：
1. 修改CI/CD平台的环境变量设置
2. 重新部署容器（无需重新构建镜像）
3. 验证新配置：`curl http://localhost/env-info`
