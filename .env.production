NODE_ENV=production
# Whether to open mock
VITE_USE_MOCK = false

VITE_GLOB_APP_TITLE = 大禹 - 问题管理平台

# public path
VITE_PUBLIC_PATH = /

# Whether to enable gzip or brotli compression
# Optional: gzip | brotli | none
# If you need multiple forms, you can use `,` to separate
VITE_BUILD_COMPRESS = 'none'

# Basic interface address SPA
VITE_GLOB_API_URL=/api

VITE_GLOB_URL="https://itmp.ttyuyin.com"
VITE_GLOB_SSO="ebc-sso.52tt.com"
VITE_GLOB_CLIENT_ID="itmp"

# File upload address， optional
# It can be forwarded by nginx or write the actual address directly
VITE_GLOB_UPLOAD_URL=/upload

# Interface prefix
VITE_GLOB_API_URL_PREFIX=
