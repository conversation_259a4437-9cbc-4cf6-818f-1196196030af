# 运行时环境配置系统

本项目支持单一镜像多环境部署，通过运行时环境变量动态配置不同环境的参数。

## 配置原理

### 1. 构建时配置
- 使用 `.env.docker` 文件作为构建时的基础配置
- 所有配置值使用占位符（如 `__VITE_GLOB_API_URL__`）
- 构建产生单一镜像，不包含具体的环境配置

### 2. 运行时配置
- 容器启动时通过环境变量传入具体配置
- `config-replacer.sh` 脚本负责替换占位符
- 支持多种环境：production、development、test、staging

## 环境变量映射

| 构建时占位符 | 运行时环境变量 | 说明 |
|-------------|---------------|------|
| `__VITE_GLOB_APP_TITLE__` | `APP_TITLE` | 应用标题 |
| `__VITE_GLOB_API_URL__` | `API_URL` | API接口地址 |
| `__VITE_GLOB_URL__` | `APP_URL` | 应用基础URL |
| `__VITE_GLOB_SSO__` | `SSO_URL` | SSO服务器地址 |
| `__VITE_GLOB_CLIENT_ID__` | `CLIENT_ID` | 客户端ID |
| `__VITE_GLOB_UPLOAD_URL__` | `UPLOAD_URL` | 文件上传地址 |
| `__VITE_GLOB_PUBLIC_PATH__` | `PUBLIC_PATH` | 公共路径 |
| `__VITE_GLOB_API_URL_PREFIX__` | `API_URL_PREFIX` | API前缀 |

## 使用方式

### 1. 代码中使用配置

```typescript
import { getAppEnvConfig } from '@/utils/env';
import { getApiBaseUrl, getSSOUrl } from '@/utils/envExample';

// 方式1：直接获取配置对象
const config = getAppEnvConfig();
console.log('API地址:', config.VITE_GLOB_API_URL);

// 方式2：使用封装的工具函数
const apiUrl = getApiBaseUrl();
const ssoUrl = getSSOUrl();
```

### 2. Docker部署配置

#### 生产环境
```bash
docker run -d \
  -e DEPLOY_ENV=production \
  -e API_URL=https://api.example.com \
  -e APP_URL=https://app.example.com \
  -e SSO_URL=sso.example.com \
  -e CLIENT_ID=prod-client \
  your-image:latest
```

#### 测试环境
```bash
docker run -d \
  -e DEPLOY_ENV=test \
  -e API_URL=https://api-test.example.com \
  -e APP_URL=https://app-test.example.com \
  -e SSO_URL=test-sso.example.com \
  -e CLIENT_ID=test-client \
  your-image:latest
```

### 3. 环境配置文件

项目包含以下环境配置文件：

- `.env.production` - 生产环境默认配置
- `.env.development` - 开发环境配置
- `.env.test` - 测试环境默认配置
- `.env.docker` - Docker构建时配置（包含占位符）

### 4. 配置优先级

配置的优先级从高到低：
1. 运行时环境变量（Docker启动时传入）
2. 环境默认配置（根据DEPLOY_ENV选择）
3. 全局默认配置

## 开发指南

### 1. 添加新的配置项

1. 在 `types/config.d.ts` 中添加类型定义：
```typescript
export interface GlobEnvConfig {
  // 新增配置项
  VITE_GLOB_NEW_CONFIG?: string;
}
```

2. 在 `.env.docker` 中添加占位符：
```bash
VITE_GLOB_NEW_CONFIG="__VITE_GLOB_NEW_CONFIG__"
```

3. 在 `config-replacer.sh` 中添加替换逻辑：
```bash
export VITE_GLOB_NEW_CONFIG="${NEW_CONFIG:-$VITE_GLOB_NEW_CONFIG}"
```

4. 在各环境配置文件中添加默认值。

### 2. 调试配置

使用内置的调试工具：
```typescript
import { logEnvInfo } from '@/utils/envExample';

// 打印当前环境配置信息
logEnvInfo();
```

## 注意事项

1. **安全性**：敏感信息（如密钥）应通过环境变量传入，不要写在配置文件中
2. **兼容性**：确保所有环境都有相应的默认配置
3. **验证**：部署前验证配置是否正确加载
4. **缓存**：配置变更后可能需要清除浏览器缓存

## 故障排除

### 配置未生效
1. 检查环境变量是否正确传入
2. 查看容器日志中的配置替换信息
3. 验证 `_app.config.js` 文件是否正确生成

### 开发环境问题
1. 确保 `.env.development` 文件存在
2. 检查 Vite 配置是否正确加载环境变量
3. 清除浏览器缓存和本地存储
