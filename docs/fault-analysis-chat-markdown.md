# 故障分析聊天组件 - Markdown 渲染功能

## 概述

故障分析聊天组件现已支持 Markdown 渲染功能，基于 `antd-design-x-vue` 和 `markdown-it` 实现，为 AI 回复提供丰富的文本格式支持。

## 功能特性

### ✅ 支持的 Markdown 语法

- **标题**: H1-H6 级别标题
- **文本格式**: 粗体、斜体、删除线
- **列表**: 有序列表、无序列表、嵌套列表
- **链接**: 自动链接识别和手动链接
- **引用**: 块引用支持
- **代码**: 行内代码和代码块
- **表格**: 完整的表格支持
- **分隔线**: 水平分隔线
- **HTML**: 安全的 HTML 标签支持

### 🎨 代码高亮支持

支持以下编程语言的语法高亮：

- JavaScript / TypeScript
- Python
- Java
- SQL
- Bash/Shell
- JSON
- XML/HTML
- CSS

## 技术实现

### 核心依赖

```json
{
  "markdown-it": "^14.1.0",
  "@types/markdown-it": "^14.1.2",
  "highlight.js": "^11.11.1"
}
```

### 配置说明

```typescript
// markdown-it 配置
const md = markdownit({
  html: true,          // 启用 HTML 标签
  breaks: true,        // 换行符转换为 <br>
  linkify: true,       // 自动识别链接
  typographer: true,   // 启用排版优化
  highlight: function (str, lang) {
    // 代码高亮配置
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(str, { 
          language: lang, 
          ignoreIllegals: true 
        }).value;
      } catch (__) {}
    }
    return '';
  },
});
```

### 自定义渲染函数

```typescript
const renderMarkdown: BubbleProps['messageRender'] = (content) =>
  h(
    Typography,
    {},
    {
      default: () =>
        h('div', {
          innerHTML: md.render(String(content || '')),
          class: 'markdown-content',
        }),
    },
  );
```

## 使用方法

### 基本使用

```vue
<template>
  <FaultAnalysisChat
    :fault-id="faultId"
    :fault-data="faultData"
    :position="{ bottom: '2rem', right: '2rem' }"
  />
</template>

<script setup>
import FaultAnalysisChat from '@/components/FaultAnalysisChat/index.vue';
</script>
```

### AI 回复示例

AI 回复的内容会自动使用 Markdown 渲染，支持以下格式：

```markdown
# 故障分析报告

## 问题概述
系统出现 **严重** 性能问题。

### 解决方案
```sql
SELECT * FROM users WHERE status = 'active';
```

| 指标 | 当前值 | 目标值 |
|------|--------|--------|
| 响应时间 | 2000ms | <200ms |
```

## 样式定制

### CSS 类名

- `.markdown-content`: 主容器
- `.hljs`: 代码高亮容器
- 标准 Markdown 元素: `h1-h6`, `p`, `ul`, `ol`, `table`, `blockquote` 等

### 自定义样式示例

```less
:deep(.markdown-content) {
  // 标题样式
  h1, h2, h3 {
    color: #1677ff;
    border-bottom: 1px solid #e8e8e8;
  }
  
  // 代码块样式
  pre {
    background-color: #f6f8fa;
    border: 1px solid #d0d7de;
    border-radius: 6px;
  }
  
  // 表格样式
  table {
    border-collapse: collapse;
    width: 100%;
  }
}
```

## 测试页面

访问测试页面查看效果：`/test/fault-analysis-chat-markdown`

测试页面提供以下功能：
- 完整 Markdown 报告测试
- 代码高亮测试
- 表格渲染测试
- 列表格式测试
- 引用样式测试
- 混合内容测试

## 注意事项

1. **安全性**: 已启用 HTML 支持，但建议对用户输入进行适当的安全过滤
2. **性能**: 大量 Markdown 内容可能影响渲染性能，建议适当控制内容长度
3. **样式**: 确保 highlight.js 样式文件正确加载
4. **兼容性**: 支持现代浏览器，IE 需要额外的 polyfill

## 扩展功能

### 添加新的编程语言支持

```typescript
import newLanguage from 'highlight.js/lib/languages/newlang';
hljs.registerLanguage('newlang', newLanguage);
```

### 自定义 Markdown 插件

```typescript
import customPlugin from 'markdown-it-custom-plugin';
md.use(customPlugin, options);
```

## 更新日志

- **v1.0.0**: 初始版本，支持基本 Markdown 渲染
- **v1.1.0**: 添加代码高亮支持
- **v1.2.0**: 优化样式和用户体验
